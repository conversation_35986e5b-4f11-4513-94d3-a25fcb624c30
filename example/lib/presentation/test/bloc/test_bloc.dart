import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:injectable/injectable.dart';

import '../../../data/model/auth/response/auth/auth_check_mail_response.dart';
import '../../../domain/entity/auth.entity.dart';
import '../../../domain/usecase/auth_check_mail.usecase.dart';
import '../../../mapper/entity/mapper.dart';
import 'bloc.dart';

@injectable
class TestBloc extends CoreV2BaseBloc<CoreV2BaseEvent, TestState> {
  TestBloc({
    required this.authCheckMailUseCase,
  }) : super(const TestState("test")) {
    on<TestEvent>(
      _onTestEvent,
    );

    on<TestCounterEvent>(
      _onTestCounterEvent,
      // ignore the same events
      transformer: distinct(),
    );

    on<AuthEmailCheck>(
      _onAuthEmailCheck,
      transformer: distinct(),
    );

    on<AuthEmailCheckWithRunCatching>(_onAuthEmailCheckWithRunCatching);

    on<TestError>(_onTestError);

    on<TestErrorWithCatching>(_onTestErrorWithCatching);
  }

  final AuthCheckMailUseCase authCheckMailUseCase;

  late final GPMapper gpMapper = GetIt.I<GPMapper>(instanceName: 'kGPMapper');

  Future _onTestEvent(TestEvent event, Emitter emit) async {
    throw Exception("test exception");
  }

  Future _onTestCounterEvent(TestCounterEvent event, Emitter emit) async {
    if (LogConfig.kLogOnStreamData || LogConfig.kLogOnStreamError) {
      logD('_onTestCounterEvent');
    }

    return emit(TestState(event.counter.toString()));
  }

  Future _onTestErrorWithCatching(
      TestErrorWithCatching event, Emitter emit) async {
    return runCatching(
      action: () async {
        addError(Exception("add an error exception"));

        throw Exception("throw exception");
      },
      handleLoading: true,
      doOnCompleted: () async => emit(const TestState("success")),
      doOnSuccessOrError: () async {
        if (LogConfig.kLogOnStreamData || LogConfig.kLogOnStreamError) {
          logD('doOnSuccessOrError');
        }
      },
      doOnError: (error, stackTrace) async {
        if (LogConfig.kLogOnStreamError) {
          logE('doOnError: $error');
        }
      },
      doOnStarted: () async {
        if (LogConfig.kLogOnStreamData) {
          logD('doOnStarted aaaa');
        }
      },
      handleRetry: true,
      maxRetries: 2,
    );
  }

  Future _onAuthEmailCheck(AuthEmailCheck event, Emitter emit) async {
    showLoading();

    await authCheckMailUseCase
        .execute(event.authCheckEmailRequest)
        .then((value) {
      if (LogConfig.kLogOnStreamData) {
        logD("value -> ${value.data.toJson()}");
      }
    }).catchError((error) {
      if (LogConfig.kLogOnStreamError) {
        logE("_onAuthEmailCheck catchError $error");
      }
    });

    await Future.delayed(const Duration(seconds: 1));

    hideLoading();

    emit(const TestState("success"));
  }

  Future _onAuthEmailCheckWithRunCatching(
      AuthEmailCheckWithRunCatching event, Emitter emit) async {
    return runCatching(
      action: () async {
        await Future.delayed(const Duration(seconds: 2));

        final response =
            await authCheckMailUseCase.execute(event.authCheckEmailRequest);

        final entity =
            _convertWithCatching<AuthCheckMailResponse, AuthCheckMailEntity>(
          <R, E>(source) {
            return gpMapper.convert<R, E>(source);
          },
          response.data,
        );

        if (LogConfig.kLogOnStreamData) {
          logD(
              'entity after map -> newDomain: ${entity.newDomain}, userId: ${entity.userId}');
        }
      },
      handleLoading: true,
      doOnCompleted: () async => emit(const TestState("success")),
      doOnSuccessOrError: () async {
        if (LogConfig.kLogOnStreamError || LogConfig.kLogOnStreamData) {
          logD('doOnSuccessOrError');
        }
      },
      doOnError: (error, stackTrace) async {
        if (LogConfig.kLogOnStreamError) {
          logE('doOnError: $error');
        }
      },
      doOnStarted: () async {
        if (LogConfig.kLogOnStreamData) {
          logD('doOnStarted aaaa');
        }
      },
      handleRetry: true,
      maxRetries: 2,
    );
  }

  Future _onTestError(TestError event, Emitter emit) async {
    // addError(Exception("test exception"));

    // throw Exception("test exception");

    return runCatching(
      action: () async {
        await Future.delayed(const Duration(seconds: 2));

        addError(Exception("add an error exception"));

        throw Exception("throw exception");
      },
      handleLoading: true,
      doOnCompleted: () async => emit(const TestState("success")),
      doOnSuccessOrError: () async {
        if (LogConfig.kLogOnStreamData || LogConfig.kLogOnStreamError) {
          logD('doOnSuccessOrError');
        }
      },
      doOnError: (error, stackTrace) async {
        if (LogConfig.kLogOnStreamError) {
          logE('doOnError: $error');
        }
      },
      doOnStarted: () async {
        if (LogConfig.kLogOnStreamData) {
          logD('doOnStarted aaaa');
        }
      },
      handleRetry: true,
      maxRetries: 2,
    );
  }

  /// convert [SOURCE] to [TARGET]
  TARGET _convertWithCatching<SOURCE, TARGET>(
    TARGET Function<SOURCE, TARGET>(SOURCE source) convertFunc,
    SOURCE source,
  ) {
    try {
      return convertFunc.call<SOURCE, TARGET>(source);
    } catch (e) {
      throw ParseException(ParseExceptionKind.errorMapping, e);
    }
  }
}
