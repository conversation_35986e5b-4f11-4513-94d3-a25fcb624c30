import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/presentation/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common_bloc.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';
import 'package:gp_core_v2/base/configs/app/app.configs.dart';
import 'package:mockito/mockito.dart';

import '../../di/modules/app_module_test.dart';
import '../../helpers/test_helper.mocks.dart';

// Test widget that implements HomeBehaviorMixin
class HomeBehaviorWidget extends StatelessWidget with HomeBehaviorMixin {
  final CommonBloc commonBloc;
  final GPAppNavigator<ExampleAppInfoRoute> appNavigator;

  const HomeBehaviorWidget({
    super.key,
    required this.commonBloc,
    required this.appNavigator,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CommonBloc>.value(
      value: commonBloc,
      child: RepositoryProvider.value(
        value: appNavigator,
        child: const SizedBox(),
      ),
    );
  }
}

void main() {
  group('HomeBehavior', () {
    late TestAppModule appModule;
    late CommonBloc commonBloc;
    late MockGPAppNavigator<ExampleAppInfoRoute> mockAppNavigator;
    late GlobalKey<NavigatorState> navigatorKey;

    setUp(() async {
      // Clear GetIt before each test
      GetIt.I.reset();

      // Register AppUseCaseManagement first (required by AppModule)
      GetIt.I.registerSingleton<AppUseCaseManagement>(AppUseCaseManagement());

      // Initialize AppModule
      appModule = TestAppModule();
      await appModule.init;

      // Register other required dependencies
      commonBloc = CommonBloc();
      mockAppNavigator = MockGPAppNavigator<ExampleAppInfoRoute>();
      navigatorKey = GlobalKey<NavigatorState>();

      GetIt.I.registerSingleton<CommonBloc>(
        commonBloc,
        instanceName: 'kCommonBloc',
      );
      GetIt.I.registerSingleton<GPAppNavigator<ExampleAppInfoRoute>>(
        mockAppNavigator,
        instanceName: 'kAppNavigator',
      );
      GetIt.I.registerSingleton<GlobalKey<NavigatorState>>(
        navigatorKey,
        instanceName: 'kAppNavigatorKey',
      );

      // Set up mock stubs
      when(mockAppNavigator.push(any, any, result: anyNamed('result'),
          useRootNavigator: anyNamed('useRootNavigator'),
          arguments: anyNamed('arguments')))
          .thenAnswer((_) async => null);

      when(mockAppNavigator.showDialog(
          context: anyNamed('context'),
          popupInfo: anyNamed('popupInfo'),
          barrierDismissible: anyNamed('barrierDismissible'),
          useSafeArea: anyNamed('useSafeArea'),
          useRootNavigator: anyNamed('useRootNavigator')))
          .thenAnswer((_) async => null);

      when(mockAppNavigator.showBaseBottomSheet(
          context: anyNamed('context'),
          popupInfo: anyNamed('popupInfo'),
          backgroundColor: anyNamed('backgroundColor'),
          elevation: anyNamed('elevation'),
          shape: anyNamed('shape'),
          clipBehavior: anyNamed('clipBehavior'),
          constraints: anyNamed('constraints'),
          isScrollControlled: anyNamed('isScrollControlled'),
          useRootNavigator: anyNamed('useRootNavigator'),
          isDismissible: anyNamed('isDismissible'),
          enableDrag: anyNamed('enableDrag'),
          showDragHandle: anyNamed('showDragHandle'),
          useSafeArea: anyNamed('useSafeArea')))
          .thenAnswer((_) async => null);
    });

    tearDown(() {
      // Close blocs before resetting GetIt
      if (!commonBloc.isClosed) {
        commonBloc.close();
      }
      // Clean up GetIt after each test
      GetIt.I.reset();
    });

    Widget createTestWidget({Widget? child}) {
      return MaterialApp(
        home: Scaffold(
          body: child ?? HomeBehaviorWidget(
            commonBloc: commonBloc,
            appNavigator: mockAppNavigator,
          ),
        ),
      );
    }

    group('Navigation', () {
      group('navigateToUserProfile', () {
        testWidgets('should navigate to user profile page', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          // Get the context from inside the widget (child of RepositoryProvider)
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.navigateToUserProfile(context);
          await tester.pumpAndSettle();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context correctly', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.navigateToUserProfile(context), returnsNormally);
        });

        testWidgets('should create user with correct data', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // The method creates a User with specific data
          // This test verifies the method executes without error
          expect(() => widget.navigateToUserProfile(context), returnsNormally);
        });
      });
    });

    group('SnackBar Methods', () {
      group('showNormalSnackBar', () {
        testWidgets('should show normal snackbar', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showNormalSnackBar(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showNormalSnackBar(context: context),
              returnsNormally);
        });
      });

      group('showSuccessSnackBar', () {
        testWidgets('should show success snackbar', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showSuccessSnackBar(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showSuccessSnackBar(context: context),
              returnsNormally);
        });
      });

      group('showErrorSnackBar', () {
        testWidgets('should show error snackbar', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showErrorSnackBar(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showErrorSnackBar(context: context),
              returnsNormally);
        });
      });
    });

    group('Dialog Methods', () {
      group('showDialogWithOneBtn', () {
        testWidgets('should show dialog with one button', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showDialogWithOneBtn(context: context);
          await tester.pump();
          await tester.pumpAndSettle(); // Ensure all timers complete

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showDialogWithOneBtn(context: context),
              returnsNormally);
        });
      });

      group('showDialogWithTwoBtn', () {
        testWidgets('should show dialog with two buttons', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showDialogWithTwoBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showDialogWithTwoBtn(context: context),
              returnsNormally);
        });
      });

      group('showDialogWithTwoVerticalBtn', () {
        testWidgets('should show dialog with two vertical buttons',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showDialogWithTwoVerticalBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showDialogWithTwoVerticalBtn(context: context),
              returnsNormally);
        });
      });
    });

    group('BottomSheet Methods', () {
      group('showSimpleBottomSheet', () {
        testWidgets('should show simple bottom sheet', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showSimpleBottomSheet(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showSimpleBottomSheet(context: context),
              returnsNormally);
        });
      });

      group('showBottomSheetWithOneBtn', () {
        testWidgets('should show bottom sheet with one button', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showBottomSheetWithOneBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showBottomSheetWithOneBtn(context: context),
              returnsNormally);
        });
      });

      group('showBottomSheetWithTwoBtn', () {
        testWidgets('should show bottom sheet with two buttons',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showBottomSheetWithTwoBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(() => widget.showBottomSheetWithTwoBtn(context: context),
              returnsNormally);
        });
      });

      group('showBottomSheetWithTwoVerticalBtn', () {
        testWidgets('should show bottom sheet with two vertical buttons',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Act
          widget.showBottomSheetWithTwoVerticalBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(SizedBox));

          // Should not throw when called with valid context
          expect(
              () => widget.showBottomSheetWithTwoVerticalBtn(context: context),
              returnsNormally);
        });
      });
    });

    group('Behavior Interface Compliance', () {
      test('HomeBehaviorWidget should implement HomeBehavior', () {
        final widget = HomeBehaviorWidget(
          commonBloc: commonBloc,
          appNavigator: mockAppNavigator,
        );
        expect(widget, isA<HomeBehavior>());
      });

      test('HomeBehaviorMixin should provide all required methods', () {
        final widget = HomeBehaviorWidget(
          commonBloc: commonBloc,
          appNavigator: mockAppNavigator,
        );

        // Verify all methods exist and are callable
        expect(widget.navigateToUserProfile, isA<Function>());
        expect(widget.showNormalSnackBar, isA<Function>());
        expect(widget.showSuccessSnackBar, isA<Function>());
        expect(widget.showErrorSnackBar, isA<Function>());
        expect(widget.showDialogWithOneBtn, isA<Function>());
        expect(widget.showDialogWithTwoBtn, isA<Function>());
        expect(widget.showDialogWithTwoVerticalBtn, isA<Function>());
        expect(widget.showSimpleBottomSheet, isA<Function>());
        expect(widget.showBottomSheetWithOneBtn, isA<Function>());
        expect(widget.showBottomSheetWithTwoBtn, isA<Function>());
        expect(widget.showBottomSheetWithTwoVerticalBtn, isA<Function>());
      });
    });

    group('Error Handling', () {
      testWidgets('should handle invalid context gracefully', (tester) async {
        final widget = HomeBehaviorWidget(
          commonBloc: commonBloc,
          appNavigator: mockAppNavigator,
        );

        // Create a detached context
        await tester.pumpWidget(const MaterialApp(home: Scaffold()));
        final context = tester.element(find.byType(Scaffold));

        // These calls might throw due to missing navigator context, which is expected behavior
        expect(() => widget.navigateToUserProfile(context), throwsA(anything));
        expect(() => widget.showNormalSnackBar(context: context),
            throwsA(anything));
      });
    });
  });
}
