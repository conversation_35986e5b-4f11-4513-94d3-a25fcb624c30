import 'package:flutter_test/flutter_test.dart';
import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/domain/entity/test.entity.dart';
import 'package:example/domain/entity/test/assignee.dart';
import 'package:gp_core_v2/base/navigator/model/route/app_route_info.dart';

void main() {
  group('ExampleAppInfoRoute', () {
    group('Route Constants', () {
      test('should have correct route constants', () {
        expect(kExampleInitial, '/');
        expect(kExampleLogin, '/login');
        expect(kExampleHome, '/home');
        expect(kExampleUserDetails, '/user');
        expect(kExampleAssigneeDetails, '/assignee');
      });
    });

    group('Initial Route', () {
      test('should create initial route with default path', () {
        // Act
        const route = ExampleAppInfoRoute.initial();

        // Assert
        expect(route.route, kExampleInitial);
        expect(route, isA<ExampleInitialRoute>());
      });

      test('should create initial route with custom path', () {
        // Act
        const route = ExampleAppInfoRoute.initial(route: '/custom');

        // Assert
        expect(route.route, '/custom');
        expect(route, isA<ExampleInitialRoute>());
      });

      test('should be equal when created with same parameters', () {
        // Arrange
        const route1 = ExampleAppInfoRoute.initial();
        const route2 = ExampleAppInfoRoute.initial();

        // Assert
        expect(route1, equals(route2));
        expect(route1.hashCode, equals(route2.hashCode));
      });
    });

    group('Login Route', () {
      test('should create login route with default path', () {
        // Act
        const route = ExampleAppInfoRoute.login();

        // Assert
        expect(route.route, kExampleLogin);
        expect(route, isA<ExampleLoginRoute>());
      });

      test('should create login route with custom path', () {
        // Act
        const route = ExampleAppInfoRoute.login(route: '/custom-login');

        // Assert
        expect(route.route, '/custom-login');
        expect(route, isA<ExampleLoginRoute>());
      });

      test('should be equal when created with same parameters', () {
        // Arrange
        const route1 = ExampleAppInfoRoute.login();
        const route2 = ExampleAppInfoRoute.login();

        // Assert
        expect(route1, equals(route2));
        expect(route1.hashCode, equals(route2.hashCode));
      });
    });

    group('Home Route', () {
      test('should create home route with default path', () {
        // Act
        const route = ExampleAppInfoRoute.home();

        // Assert
        expect(route.route, kExampleHome);
        expect(route, isA<ExampleHomeRoute>());
      });

      test('should create home route with custom path', () {
        // Act
        const route = ExampleAppInfoRoute.home(route: '/custom-home');

        // Assert
        expect(route.route, '/custom-home');
        expect(route, isA<ExampleHomeRoute>());
      });

      test('should be equal when created with same parameters', () {
        // Arrange
        const route1 = ExampleAppInfoRoute.home();
        const route2 = ExampleAppInfoRoute.home();

        // Assert
        expect(route1, equals(route2));
        expect(route1.hashCode, equals(route2.hashCode));
      });
    });

    group('User Profile Route', () {
      test('should create user profile route with required user', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');

        // Act
        const route = ExampleAppInfoRoute.userProfile(user: user);

        // Assert
        expect(route.route, kExampleUserDetails);
        expect(route, isA<ExampleUserRoute>());
        expect((route as ExampleUserRoute).user, user);
      });

      test('should create user profile route with custom path', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');

        // Act
        const route = ExampleAppInfoRoute.userProfile(
          route: '/custom-user',
          user: user,
        );

        // Assert
        expect(route.route, '/custom-user');
        expect((route as ExampleUserRoute).user, user);
      });

      test('should be equal when created with same parameters', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        const route1 = ExampleAppInfoRoute.userProfile(user: user);
        const route2 = ExampleAppInfoRoute.userProfile(user: user);

        // Assert
        expect(route1, equals(route2));
        expect(route1.hashCode, equals(route2.hashCode));
      });

      test('should not be equal when created with different users', () {
        // Arrange
        const user1 = User(id: 1, name: 'Test User 1');
        const user2 = User(id: 2, name: 'Test User 2');
        const route1 = ExampleAppInfoRoute.userProfile(user: user1);
        const route2 = ExampleAppInfoRoute.userProfile(user: user2);

        // Assert
        expect(route1, isNot(equals(route2)));
      });
    });

    group('Assignee Details Route', () {
      test('should create assignee details route with required entity', () {
        // Arrange
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');

        // Act
        final route = ExampleAppInfoRoute.assigneeDetails(entity: assignee);

        // Assert
        expect(route.route, kExampleAssigneeDetails);
        expect(route, isA<ExampleAssigneeRoute>());
        expect((route as ExampleAssigneeRoute).entity, assignee);
      });

      test('should create assignee details route with custom path', () {
        // Arrange
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');

        // Act
        final route = ExampleAppInfoRoute.assigneeDetails(
          route: '/custom-assignee',
          entity: assignee,
        );

        // Assert
        expect(route.route, '/custom-assignee');
        expect((route as ExampleAssigneeRoute).entity, assignee);
      });

      test('should be equal when created with same parameters', () {
        // Arrange
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');
        final route1 = ExampleAppInfoRoute.assigneeDetails(entity: assignee);
        final route2 = ExampleAppInfoRoute.assigneeDetails(entity: assignee);

        // Assert
        expect(route1, equals(route2));
        expect(route1.hashCode, equals(route2.hashCode));
      });

      test('should not be equal when created with different assignees', () {
        // Arrange
        final assignee1 = AssigneeEntity(id: 1, displayName: 'Test Assignee 1');
        final assignee2 = AssigneeEntity(id: 2, displayName: 'Test Assignee 2');
        final route1 = ExampleAppInfoRoute.assigneeDetails(entity: assignee1);
        final route2 = ExampleAppInfoRoute.assigneeDetails(entity: assignee2);

        // Assert
        expect(route1, isNot(equals(route2)));
      });
    });

    group('Route Type Checking', () {
      test('should correctly identify route types', () {
        // Arrange
        const initialRoute = ExampleAppInfoRoute.initial();
        const loginRoute = ExampleAppInfoRoute.login();
        const homeRoute = ExampleAppInfoRoute.home();
        const userRoute = ExampleAppInfoRoute.userProfile(user: User(id: 1, name: 'Test'));
        final assigneeRoute = ExampleAppInfoRoute.assigneeDetails(entity: AssigneeEntity(id: 1, displayName: 'Test'));

        // Assert
        expect(initialRoute, isA<ExampleInitialRoute>());
        expect(loginRoute, isA<ExampleLoginRoute>());
        expect(homeRoute, isA<ExampleHomeRoute>());
        expect(userRoute, isA<ExampleUserRoute>());
        expect(assigneeRoute, isA<ExampleAssigneeRoute>());
      });

      test('should implement GPAppRouteInfo', () {
        // Arrange
        const route = ExampleAppInfoRoute.initial();

        // Assert
        expect(route, isA<GPAppRouteInfo>());
      });
    });

    group('Serialization', () {
      test('should support toString', () {
        // Arrange
        const route = ExampleAppInfoRoute.initial();

        // Act
        final string = route.toString();

        // Assert
        expect(string, isA<String>());
        expect(string, contains('ExampleAppInfoRoute'));
      });

      test('should support copyWith for routes with parameters', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        const route = ExampleAppInfoRoute.userProfile(user: user);

        // Act
        final copiedRoute = (route as ExampleUserRoute).copyWith(route: '/new-path');

        // Assert
        expect(copiedRoute.route, '/new-path');
        expect(copiedRoute.user, user);
      });
    });
  });
}
